#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق حساب يومية محلات الهواتف
Phone Shop Daily Calculator Application
"""

import tkinter as tk
from tkinter import messagebox, ttk, filedialog
import sqlite3
from datetime import datetime
import os

class PhoneShopApp:
    def __init__(self, root):
        self.root = root
        self.root.title("🏪 حساب يومية محل الهواتف - Phone Shop Calculator")

        # ضبط أبعاد الشاشة
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # حساب الأبعاد المناسبة (90% من الشاشة)
        width = int(screen_width * 0.9)
        height = int(screen_height * 0.9)

        # توسيط النافذة
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2

        self.root.geometry(f"{width}x{height}+{x}+{y}")
        self.root.minsize(1200, 800)  # حد أدنى للحجم

        # تحديد الثيم الحالي
        self.current_theme = "default"

        # ألوان النظام - الثيمات المختلفة
        self.themes = {
            'default': {
                'name': 'شكل 1',
                'colors': {
                    'primary': '#2E86AB',      # أزرق رئيسي
                    'secondary': '#A23B72',    # بنفسجي ثانوي
                    'accent': '#F18F01',       # برتقالي للتمييز
                    'success': '#C73E1D',      # أحمر للنجاح
                    'info': '#17A2B8',         # أزرق معلومات
                    'danger': '#DC3545',       # أحمر خطر
                    'warning': '#F4A261',      # تحذير
                    'bg_main': '#F5F7FA',      # خلفية رئيسية فاتحة
                    'bg_card': '#FFFFFF',      # خلفية البطاقات
                    'text_dark': '#2D3748',    # نص داكن
                    'text_light': '#718096',   # نص فاتح
                    'border': '#E2E8F0'        # حدود
                }
            },
            'modern_dark': {
                'name': 'شكل 2',
                'colors': {
                    'primary': '#00C3FF',      # أزرق سماوي عصري
                    'secondary': '#1A1A2E',    # أزرق داكن
                    'accent': '#FF6B6B',       # أحمر مرجاني
                    'success': '#00C3FF',      # أزرق للنجاح
                    'info': '#00C3FF',         # أزرق للمعلومات
                    'danger': '#FF6B6B',       # أحمر للخطر
                    'warning': '#FF6B6B',      # أحمر للتحذير
                    'bg_main': '#121212',      # خلفية داكنة عصرية
                    'bg_card': '#2C2C3A',      # خلفية البطاقات
                    'text_dark': '#E0E0E0',    # نص فاتح
                    'text_light': '#B0B0B0',   # نص رمادي فاتح
                    'border': '#333333'        # حدود رمادية
                }
            },
            'professional': {
                'name': 'شكل 3',
                'colors': {
                    'primary': '#2c3e50',      # أزرق داكن أنيق
                    'secondary': '#34495e',    # رمادي أزرق
                    'accent': '#e74c3c',       # أحمر أنيق
                    'success': '#27ae60',      # أخضر مهني
                    'info': '#3498db',         # أزرق معلومات
                    'danger': '#e74c3c',       # أحمر خطر
                    'warning': '#f39c12',      # برتقالي تحذير
                    'bg_main': '#ecf0f1',      # خلفية رمادية فاتحة جداً
                    'bg_card': '#ffffff',      # خلفية البطاقات بيضاء نقية
                    'text_dark': '#2c3e50',    # نص داكن أنيق
                    'text_light': '#7f8c8d',   # نص رمادي فاتح
                    'border': '#bdc3c7',       # حدود رمادية ناعمة
                    'sidebar_bg': '#34495e',    # خلفية الشريط الجانبي
                    'sidebar_text': '#ecf0f1',  # نص الشريط الجانبي
                    'sidebar_hover': '#2c3e50', # تمرير الشريط الجانبي
                    'sidebar_active': '#e74c3c' # نشط الشريط الجانبي
                }
            },
            'vip': {
                'name': 'شكل 4',
                'colors': {
                    'primary': '#8B5CF6',      # بنفسجي ملكي
                    'secondary': '#A78BFA',    # بنفسجي فاتح
                    'accent': '#F59E0B',       # ذهبي لامع
                    'success': '#10B981',      # أخضر زمردي
                    'info': '#06B6D4',         # سماوي لامع
                    'danger': '#EF4444',       # أحمر ناري
                    'warning': '#F59E0B',      # ذهبي تحذير
                    'bg_main': '#0F0F23',      # خلفية داكنة عميقة
                    'bg_card': '#1E1E3F',      # بطاقات داكنة أنيقة
                    'text_dark': '#F8FAFC',    # نص أبيض لامع
                    'text_light': '#CBD5E1',   # نص رمادي فاتح
                    'border': '#374151',       # حدود رمادية داكنة
                    'sidebar_bg': '#0F0F23',     # شريط جانبي داكن
                    'sidebar_text': '#F8FAFC',   # نص شريط جانبي
                    'sidebar_hover': '#8B5CF6',  # تمرير بنفسجي
                    'sidebar_active': '#F59E0B', # نشط ذهبي
                }
            },
            'neon_tech': {
                'name': 'شكل 5',
                'colors': {
                    'primary': '#1E3A8A',      # أزرق داكن أنيق
                    'secondary': '#1F2937',    # رمادي داكن
                    'accent': '#3B82F6',       # أزرق لامع
                    'success': '#10B981',      # أخضر أنيق
                    'info': '#06B6D4',         # سماوي هادئ
                    'danger': '#EF4444',       # أحمر أنيق
                    'warning': '#F59E0B',      # برتقالي ذهبي
                    'bg_main': '#111827',      # خلفية داكنة أنيقة
                    'bg_card': '#1F2937',      # بطاقات رمادية داكنة
                    'text_dark': '#F9FAFB',    # نص أبيض ناعم
                    'text_light': '#D1D5DB',   # نص رمادي فاتح
                    'border': '#374151',       # حدود رمادية
                    'sidebar_bg': '#1F2937',   # شريط جانبي رمادي داكن
                    'sidebar_text': '#F9FAFB', # نص شريط جانبي أبيض
                    'sidebar_hover': '#374151', # تمرير رمادي
                    'sidebar_active': '#3B82F6', # نشط أزرق لامع
                    'led_active': '#06B6D4',   # LED نشط سماوي
                    'led_hover': '#3B82F6'     # LED تمرير أزرق
                }
            }
        }

        # تطبيق الثيم الحالي
        self.colors = self.themes[self.current_theme]['colors']

        # للتوافق مع النظام القديم
        self.is_dark_mode = False
        self.light_colors = self.themes['default']['colors']
        self.dark_colors = self.themes['modern_dark']['colors']

        self.root.configure(bg=self.colors['bg_main'])

        # إعدادات التطبيق
        self.app_name = "حساب يومية محل الهواتف"
        self.app_logo = None  # مسار الشعار

        # متغيرات التطبيق
        self.selected_category = None
        self.selected_item = None
        self.categories_data = {}
        self.current_view = 'main'

        # إنشاء قاعدة البيانات
        self.init_database()

        # تحميل البيانات الأولية
        self.load_initial_data()

        # إنشاء الواجهة
        self.create_modern_interface()

    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        self.conn = sqlite3.connect('phone_shop.db')
        self.cursor = self.conn.cursor()

        # جدول الفئات الرئيسية
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                name_ar TEXT NOT NULL
            )
        ''')

        # جدول العناصر الفرعية
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category_id INTEGER,
                name TEXT NOT NULL,
                name_ar TEXT NOT NULL,
                price REAL DEFAULT 0,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')

        # جدول المعاملات اليومية
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                category_id INTEGER,
                item_id INTEGER,
                quantity INTEGER DEFAULT 1,
                price REAL NOT NULL,
                total REAL NOT NULL,
                notes TEXT,
                customer_name TEXT,
                customer_phone TEXT,
                FOREIGN KEY (category_id) REFERENCES categories (id),
                FOREIGN KEY (item_id) REFERENCES items (id)
            )
        ''')

        # جدول طلبات الصيانة
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS repair_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date TEXT NOT NULL,
                repair_type TEXT NOT NULL,
                device_model TEXT,
                problem_description TEXT,
                customer_name TEXT,
                customer_phone TEXT,
                price REAL DEFAULT 0,
                notes TEXT,
                status TEXT DEFAULT 'pending'
            )
        ''')

        # جدول فئات المخازن
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS warehouse_categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_date TEXT NOT NULL
            )
        ''')

        # جدول عناصر المخازن
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS warehouse_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category_id INTEGER,
                name TEXT NOT NULL,
                quantity INTEGER DEFAULT 0,
                price REAL DEFAULT 0,
                notes TEXT,
                created_date TEXT NOT NULL,
                FOREIGN KEY (category_id) REFERENCES warehouse_categories (id)
            )
        ''')

        # جدول الحساب المؤجل
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS deferred_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_name TEXT NOT NULL,
                customer_phone TEXT,
                total_amount REAL DEFAULT 0,
                paid_amount REAL DEFAULT 0,
                remaining_amount REAL DEFAULT 0,
                created_date TEXT NOT NULL,
                last_payment_date TEXT,
                notes TEXT
            )
        ''')

        # جدول معاملات الحساب المؤجل
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS deferred_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_id INTEGER,
                transaction_type TEXT NOT NULL,
                amount REAL NOT NULL,
                item_name TEXT,
                quantity INTEGER DEFAULT 1,
                date TEXT NOT NULL,
                notes TEXT,
                FOREIGN KEY (account_id) REFERENCES deferred_accounts (id)
            )
        ''')

        # جدول الإعدادات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT NOT NULL UNIQUE,
                value TEXT
            )
        ''')

        self.conn.commit()

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        # إضافة الفئات الرئيسية
        categories = [
            ('phones', 'بيع هواتف'),
            ('accessories', 'اكسسوارات'),
            ('hardware_repair', 'صيانة هاردوير'),
            ('software_repair', 'صيانة سوفتوير'),
            ('bug_repair', 'صيانة باغات'),
            ('cash', 'كاش'),
            ('fawry', 'فوري'),
            ('warehouse', 'مخازن'),
            ('expenses', 'في الخارج'),
            ('liquidity', 'إضافة سيولة'),
            ('deferred_account', 'حساب مؤجل')
        ]

        for cat_name, cat_name_ar in categories:
            self.cursor.execute('''
                INSERT OR IGNORE INTO categories (name, name_ar) VALUES (?, ?)
            ''', (cat_name, cat_name_ar))

        self.conn.commit()

        # حذف فئات المخازن الموجودة حالياً
        self.clear_warehouse_categories()

        # حذف البيانات الحالية حسب الطلب
        self.clear_current_data()

    def clear_warehouse_categories(self):
        """حذف جميع فئات المخازن الموجودة حالياً"""
        try:
            # حذف جميع أصناف المخازن أولاً
            self.cursor.execute('DELETE FROM warehouse_items')
            # حذف جميع فئات المخازن
            self.cursor.execute('DELETE FROM warehouse_categories')
            self.conn.commit()
            print("تم حذف جميع فئات المخازن الموجودة")
        except Exception as e:
            print(f"خطأ في حذف فئات المخازن: {e}")

    def clear_current_data(self):
        """حذف البيانات الحالية حسب الطلب"""
        try:
            # حذف طلبات الصيانة الموجودة
            self.cursor.execute("DELETE FROM repair_requests WHERE repair_type IN ('hardware', 'software', 'bug')")
            
            # حذف المصروفات الخارجية الموجودة
            self.cursor.execute("DELETE FROM transactions WHERE category_id = (SELECT id FROM categories WHERE name = 'expenses')")
            
            self.conn.commit()
            print("تم حذف البيانات الحالية")
        except Exception as e:
            print(f"خطأ في حذف البيانات الحالية: {e}")

    def create_modern_interface(self):
        """إنشاء الواجهة الحديثة"""
        # إعداد الشبكة الرئيسية
        self.root.grid_rowconfigure(0, weight=1)
        self.root.grid_columnconfigure(0, weight=1)

        # الحاوي الرئيسي
        main_container = tk.Frame(self.root, bg=self.colors['bg_main'])
        main_container.grid(row=0, column=0, sticky="nsew", padx=10, pady=10)
        main_container.grid_rowconfigure(1, weight=1)
        main_container.grid_columnconfigure(1, weight=1)

        # شريط العنوان
        self.create_header(main_container)

        # الشريط الجانبي للفئات
        self.create_sidebar(main_container)

        # المنطقة الرئيسية
        self.create_main_area(main_container)

        # تحميل الفئات
        self.load_categories_data()

    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=80)
        header_frame.grid(row=0, column=0, columnspan=2, sticky="ew", pady=(0, 10))
        header_frame.grid_propagate(False)

        # إطار اليسار (الشعار + اسم التطبيق)
        left_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20)

        # الشعار
        if self.app_logo and os.path.exists(self.app_logo):
            try:
                # محاولة استخدام PIL
                from PIL import Image, ImageTk
                img = Image.open(self.app_logo)
                img = img.resize((60, 60), Image.Resampling.LANCZOS)
                self.logo_photo = ImageTk.PhotoImage(img)
                logo_label = tk.Label(left_frame, image=self.logo_photo, bg=self.colors['primary'])
                logo_label.pack(side=tk.LEFT, padx=(0, 15), pady=10)
            except ImportError:
                # في حالة عدم وجود PIL، استخدم رمز بسيط
                logo_label = tk.Label(
                    left_frame,
                    text="🏪",
                    font=('Arial', 24),
                    fg='white',
                    bg=self.colors['primary']
                )
                logo_label.pack(side=tk.LEFT, padx=(0, 15), pady=10)
            except:
                pass

        # اسم التطبيق
        title_label = tk.Label(
            left_frame,
            text=self.app_name,
            font=('Arial', 20, 'bold'),
            fg='white',
            bg=self.colors['primary']
        )
        title_label.pack(side=tk.LEFT, pady=20)

        # إطار اليمين (التاريخ + زر الثيم)
        right_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20)

        # التاريخ
        today = datetime.now().strftime('%Y-%m-%d')
        date_label = tk.Label(
            right_frame,
            text=f"📅 {today}",
            font=('Arial', 14, 'bold'),
            fg='white',
            bg=self.colors['primary']
        )
        date_label.pack(side=tk.RIGHT, pady=25, padx=(0, 20))

        # زر تبديل الثيم
        theme_btn = tk.Button(
            right_frame,
            text="🌙" if self.current_theme == "default" else "☀️",
            font=('Arial', 16),
            bg=self.colors['accent'],
            fg='white',
            relief=tk.FLAT,
            width=3,
            command=self.toggle_theme,
            cursor='hand2'
        )
        theme_btn.pack(side=tk.RIGHT, pady=20)

    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        sidebar_frame = tk.Frame(parent, bg=self.colors.get('sidebar_bg', self.colors['secondary']), width=250)
        sidebar_frame.grid(row=1, column=0, sticky="nsew", padx=(0, 10))
        sidebar_frame.grid_propagate(False)

        # عنوان الشريط الجانبي
        sidebar_title = tk.Label(
            sidebar_frame,
            text="📋 الأقسام الرئيسية",
            font=('Arial', 16, 'bold'),
            fg=self.colors.get('sidebar_text', 'white'),
            bg=self.colors.get('sidebar_bg', self.colors['secondary'])
        )
        sidebar_title.pack(pady=20)

        # قائمة الفئات
        self.categories_frame = tk.Frame(sidebar_frame, bg=self.colors.get('sidebar_bg', self.colors['secondary']))
        self.categories_frame.pack(fill=tk.BOTH, expand=True, padx=10)

    def create_main_area(self, parent):
        """إنشاء المنطقة الرئيسية"""
        self.main_area = tk.Frame(parent, bg=self.colors['bg_main'])
        self.main_area.grid(row=1, column=1, sticky="nsew")

        # عرض الصفحة الرئيسية
        self.show_main_dashboard()

    def load_categories_data(self):
        """تحميل بيانات الفئات"""
        # مسح الفئات الحالية
        for widget in self.categories_frame.winfo_children():
            widget.destroy()

        # جلب الفئات من قاعدة البيانات
        self.cursor.execute('SELECT id, name, name_ar FROM categories ORDER BY id')
        categories = self.cursor.fetchall()

        # إضافة أزرار الفئات
        for cat_id, cat_name, cat_name_ar in categories:
            self.create_category_button(cat_id, cat_name, cat_name_ar)

    def create_category_button(self, cat_id, cat_name, cat_name_ar):
        """إنشاء زر فئة"""
        # تحديد الأيقونة حسب نوع الفئة
        icons = {
            'phones': '📱',
            'accessories': '🔌',
            'hardware_repair': '🔧',
            'software_repair': '💻',
            'bug_repair': '🐛',
            'cash': '💰',
            'fawry': '⚡',
            'warehouse': '📦',
            'expenses': '🌍',
            'liquidity': '💵',
            'deferred_account': '📋'
        }

        icon = icons.get(cat_name, '📁')

        btn = tk.Button(
            self.categories_frame,
            text=f"{icon} {cat_name_ar}",
            font=('Arial', 12, 'bold'),
            bg=self.colors.get('sidebar_bg', self.colors['secondary']),
            fg=self.colors.get('sidebar_text', 'white'),
            relief=tk.FLAT,
            anchor='w',
            padx=20,
            pady=10,
            cursor='hand2',
            command=lambda: self.show_category_items(cat_id, cat_name_ar)
        )
        btn.pack(fill=tk.X, pady=2)

        # تأثير التمرير
        def on_enter(e):
            btn.config(bg=self.colors.get('sidebar_hover', self.colors['accent']))

        def on_leave(e):
            btn.config(bg=self.colors.get('sidebar_bg', self.colors['secondary']))

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)

    def toggle_theme(self):
        """تبديل الثيم"""
        if self.current_theme == "default":
            self.current_theme = "modern_dark"
        else:
            self.current_theme = "default"

        self.colors = self.themes[self.current_theme]['colors']
        self.root.configure(bg=self.colors['bg_main'])

        # إعادة إنشاء الواجهة
        self.create_modern_interface()

    def show_main_dashboard(self):
        """عرض لوحة التحكم الرئيسية"""
        # مسح المحتوى الحالي
        for widget in self.main_area.winfo_children():
            widget.destroy()

        # إطار رئيسي
        dashboard_frame = tk.Frame(self.main_area, bg=self.colors['bg_main'])
        dashboard_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان لوحة التحكم
        title_label = tk.Label(
            dashboard_frame,
            text="📊 لوحة التحكم الرئيسية",
            font=('Arial', 24, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_main']
        )
        title_label.pack(pady=20)

        # إحصائيات سريعة
        self.create_quick_stats(dashboard_frame)

        # معاملات اليوم
        self.create_daily_transactions(dashboard_frame)

        # قسم دعم فلسطين مع الإعدادات
        self.create_palestine_section(dashboard_frame)

    def create_quick_stats(self, parent):
        """إنشاء الإحصائيات السريعة"""
        stats_frame = tk.Frame(parent, bg=self.colors['bg_main'])
        stats_frame.pack(fill=tk.X, pady=20)

        # عنوان الإحصائيات
        stats_title = tk.Label(
            stats_frame,
            text="📈 إحصائيات اليوم",
            font=('Arial', 18, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_main']
        )
        stats_title.pack(pady=(0, 15))

        # إطار الإحصائيات
        stats_container = tk.Frame(stats_frame, bg=self.colors['bg_main'])
        stats_container.pack(fill=tk.X)

        # حساب الإحصائيات
        today = datetime.now().strftime('%Y-%m-%d')

        # إجمالي المعاملات
        self.cursor.execute('SELECT SUM(total), COUNT(*) FROM transactions WHERE date LIKE ?', (f'{today}%',))
        total_amount, total_count = self.cursor.fetchone()
        total_amount = total_amount or 0
        total_count = total_count or 0

        # إجمالي طلبات الصيانة
        self.cursor.execute('SELECT SUM(price), COUNT(*) FROM repair_requests WHERE date LIKE ?', (f'{today}%',))
        repair_amount, repair_count = self.cursor.fetchone()
        repair_amount = repair_amount or 0
        repair_count = repair_count or 0

        # إجمالي اليوم
        daily_total = total_amount + repair_amount
        daily_transactions = total_count + repair_count

        # بطاقات الإحصائيات
        stats_data = [
            ("💰 إجمالي اليوم", f"{daily_total:.2f} جنيه", self.colors['success']),
            ("📊 عدد المعاملات", str(daily_transactions), self.colors['info']),
            ("🔧 طلبات الصيانة", str(repair_count), self.colors['warning']),
            ("📱 مبيعات المنتجات", f"{total_amount:.2f} جنيه", self.colors['primary'])
        ]

        for i, (title, value, color) in enumerate(stats_data):
            stat_card = tk.Frame(stats_container, bg=color, relief=tk.RAISED, bd=2)
            stat_card.grid(row=0, column=i, padx=10, pady=5, sticky="ew")
            stats_container.grid_columnconfigure(i, weight=1)

            tk.Label(
                stat_card,
                text=title,
                font=('Arial', 12, 'bold'),
                fg='white',
                bg=color
            ).pack(pady=(10, 5))

            tk.Label(
                stat_card,
                text=value,
                font=('Arial', 16, 'bold'),
                fg='white',
                bg=color
            ).pack(pady=(0, 10))

    def create_daily_transactions(self, parent):
        """إنشاء قسم معاملات اليوم"""
        trans_frame = tk.Frame(parent, bg=self.colors['bg_main'])
        trans_frame.pack(fill=tk.BOTH, expand=True, pady=20)

        # عنوان المعاملات
        trans_title = tk.Label(
            trans_frame,
            text="📋 معاملات اليوم",
            font=('Arial', 18, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_main']
        )
        trans_title.pack(pady=(0, 15))

        # إطار الجدول مع شريط التمرير
        table_frame = tk.Frame(trans_frame, bg=self.colors['bg_card'], relief=tk.RAISED, bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True)

        # شريط التمرير العمودي
        v_scrollbar = tk.Scrollbar(table_frame, orient=tk.VERTICAL)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # شريط التمرير الأفقي
        h_scrollbar = tk.Scrollbar(table_frame, orient=tk.HORIZONTAL)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # جدول المعاملات
        columns = ('الوقت', 'النوع', 'التفاصيل', 'المبلغ', 'الملاحظات')
        self.transactions_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            yscrollcommand=v_scrollbar.set,
            xscrollcommand=h_scrollbar.set
        )

        # تكوين أعمدة الجدول
        for col in columns:
            self.transactions_tree.heading(col, text=col)
            self.transactions_tree.column(col, width=150, anchor='center')

        self.transactions_tree.pack(fill=tk.BOTH, expand=True)

        # ربط أشرطة التمرير
        v_scrollbar.config(command=self.transactions_tree.yview)
        h_scrollbar.config(command=self.transactions_tree.xview)

        # تحميل معاملات اليوم
        self.load_daily_transactions()

    def load_daily_transactions(self):
        """تحميل معاملات اليوم"""
        # مسح البيانات الحالية
        for item in self.transactions_tree.get_children():
            self.transactions_tree.delete(item)

        today = datetime.now().strftime('%Y-%m-%d')

        # جلب المعاملات العادية
        self.cursor.execute('''
            SELECT t.date, c.name_ar, i.name_ar, t.total, t.notes
            FROM transactions t
            JOIN categories c ON t.category_id = c.id
            LEFT JOIN items i ON t.item_id = i.id
            WHERE t.date LIKE ?
            ORDER BY t.date DESC
        ''', (f'{today}%',))

        for row in self.cursor.fetchall():
            date_time = row[0]
            time_only = date_time.split(' ')[1] if ' ' in date_time else date_time
            category = row[1]
            item = row[2] or 'عام'
            amount = f"{row[3]:.2f} جنيه"
            notes = row[4] or ''

            self.transactions_tree.insert('', 'end', values=(time_only, category, item, amount, notes))

        # جلب طلبات الصيانة
        self.cursor.execute('''
            SELECT date, repair_type, device_model, price, notes
            FROM repair_requests
            WHERE date LIKE ?
            ORDER BY date DESC
        ''', (f'{today}%',))

        repair_types = {
            'hardware': 'صيانة هاردوير',
            'software': 'صيانة سوفتوير',
            'bug': 'صيانة باغات'
        }

        for row in self.cursor.fetchall():
            date_time = row[0]
            time_only = date_time.split(' ')[1] if ' ' in date_time else date_time
            repair_type = repair_types.get(row[1], row[1])
            device = row[2] or 'غير محدد'
            amount = f"{row[3]:.2f} جنيه"
            notes = row[4] or ''

            self.transactions_tree.insert('', 'end', values=(time_only, repair_type, device, amount, notes))

    def create_palestine_section(self, parent):
        """إنشاء قسم دعم فلسطين مع الإعدادات"""
        palestine_frame = tk.Frame(parent, bg=self.colors['bg_main'])
        palestine_frame.pack(fill=tk.X, pady=20)

        # إطار دعم فلسطين
        support_frame = tk.Frame(palestine_frame, bg=self.colors['bg_card'], relief=tk.RAISED, bd=2)
        support_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # صورة العلم الفلسطيني
        flag_path = "free-palestine-klistremerke.jpg"
        if os.path.exists(flag_path):
            try:
                from PIL import Image, ImageTk
                img = Image.open(flag_path)
                img = img.resize((120, 80), Image.Resampling.LANCZOS)
                self.flag_photo = ImageTk.PhotoImage(img)
                flag_label = tk.Label(support_frame, image=self.flag_photo, bg=self.colors['bg_card'])
                flag_label.pack(pady=20)
            except ImportError:
                # في حالة عدم وجود PIL، استخدم رمز العلم
                flag_label = tk.Label(
                    support_frame,
                    text="🇵🇸",
                    font=('Arial', 48),
                    bg=self.colors['bg_card']
                )
                flag_label.pack(pady=20)
            except:
                # في حالة عدم وجود الصورة
                flag_label = tk.Label(
                    support_frame,
                    text="🇵🇸",
                    font=('Arial', 48),
                    bg=self.colors['bg_card']
                )
                flag_label.pack(pady=20)
        else:
            # في حالة عدم وجود الصورة، استخدم رمز العلم
            flag_label = tk.Label(
                support_frame,
                text="🇵🇸",
                font=('Arial', 48),
                bg=self.colors['bg_card']
            )
            flag_label.pack(pady=20)

        # نص الدعم
        support_text = tk.Label(
            support_frame,
            text="نحن ندعم فلسطين",
            font=('Arial', 20, 'bold'),
            fg='#2E8B57',
            bg=self.colors['bg_card']
        )
        support_text.pack(pady=20)

        # إطار الإعدادات
        settings_frame = tk.Frame(palestine_frame, bg=self.colors['bg_card'], relief=tk.RAISED, bd=2)
        settings_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))

        # عنوان الإعدادات
        settings_title = tk.Label(
            settings_frame,
            text="⚙️ الإعدادات",
            font=('Arial', 16, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_card']
        )
        settings_title.pack(pady=20)

        # زر إعدادات التطبيق
        app_settings_btn = tk.Button(
            settings_frame,
            text="🏪 إعدادات التطبيق",
            font=('Arial', 12, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2',
            command=self.show_app_settings
        )
        app_settings_btn.pack(pady=10, padx=20, fill=tk.X)

        # زر الثيمات
        themes_btn = tk.Button(
            settings_frame,
            text="🎨 أشكال",
            font=('Arial', 12, 'bold'),
            bg=self.colors['accent'],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2',
            command=self.show_themes_settings
        )
        themes_btn.pack(pady=10, padx=20, fill=tk.X)

        # زر التقارير
        reports_btn = tk.Button(
            settings_frame,
            text="📊 التقارير",
            font=('Arial', 12, 'bold'),
            bg=self.colors['info'],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2',
            command=self.show_reports
        )
        reports_btn.pack(pady=10, padx=20, fill=tk.X)

    def show_app_settings(self):
        """عرض إعدادات التطبيق"""
        messagebox.showinfo("الإعدادات", "إعدادات التطبيق ستكون متاحة قريباً")

    def show_themes_settings(self):
        """عرض إعدادات الثيمات"""
        # نافذة الثيمات
        themes_window = tk.Toplevel(self.root)
        themes_window.title("🎨 أشكال التطبيق")
        themes_window.geometry("400x500")
        themes_window.configure(bg=self.colors['bg_main'])

        # عنوان النافذة
        title_label = tk.Label(
            themes_window,
            text="🎨 اختر الشكل المفضل",
            font=('Arial', 18, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_main']
        )
        title_label.pack(pady=20)

        # أزرار الثيمات
        for theme_key, theme_data in self.themes.items():
            theme_btn = tk.Button(
                themes_window,
                text=theme_data['name'],
                font=('Arial', 14, 'bold'),
                bg=theme_data['colors']['primary'],
                fg='white',
                relief=tk.FLAT,
                padx=30,
                pady=15,
                cursor='hand2',
                command=lambda t=theme_key: self.apply_theme(t, themes_window)
            )
            theme_btn.pack(pady=10, fill=tk.X, padx=50)

    def apply_theme(self, theme_key, window):
        """تطبيق ثيم جديد"""
        self.current_theme = theme_key
        self.colors = self.themes[theme_key]['colors']
        self.root.configure(bg=self.colors['bg_main'])

        # إغلاق نافذة الثيمات
        window.destroy()

        # إعادة إنشاء الواجهة
        self.create_modern_interface()

        messagebox.showinfo("تم التطبيق", f"تم تطبيق {self.themes[theme_key]['name']} بنجاح!")

    def show_reports(self):
        """عرض التقارير"""
        messagebox.showinfo("التقارير", "التقارير ستكون متاحة قريباً")

    def show_category_items(self, cat_id, cat_name):
        """عرض عناصر الفئة"""
        # مسح المحتوى الحالي
        for widget in self.main_area.winfo_children():
            widget.destroy()

        # إطار رئيسي
        category_frame = tk.Frame(self.main_area, bg=self.colors['bg_main'])
        category_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان الفئة
        title_label = tk.Label(
            category_frame,
            text=f"📋 {cat_name}",
            font=('Arial', 20, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_main']
        )
        title_label.pack(pady=20)

        # زر العودة
        back_btn = tk.Button(
            category_frame,
            text="🔙 العودة للرئيسية",
            font=('Arial', 12, 'bold'),
            bg=self.colors['secondary'],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2',
            command=self.show_main_dashboard
        )
        back_btn.pack(anchor='w', pady=(0, 20))

        # عرض محتوى الفئة حسب نوعها
        if cat_name in ["صيانة هاردوير", "صيانة سوفتوير", "صيانة باغات"]:
            self.show_repair_section(category_frame, cat_name)
        elif cat_name == "مخازن":
            self.show_warehouse_section(category_frame)
        elif cat_name == "حساب مؤجل":
            self.show_deferred_account_section(category_frame)
        elif cat_name in ["كاش", "فوري", "في الخارج", "إضافة سيولة"]:
            self.show_financial_section(category_frame, cat_name)
        else:
            self.show_products_section(category_frame, cat_id, cat_name)

    def show_repair_section(self, parent, repair_type):
        """عرض قسم الصيانة"""
        # إطار الصيانة
        repair_frame = tk.Frame(parent, bg=self.colors['bg_main'])
        repair_frame.pack(fill=tk.BOTH, expand=True)

        # نموذج إضافة طلب صيانة
        form_frame = tk.Frame(repair_frame, bg=self.colors['bg_card'], relief=tk.RAISED, bd=2)
        form_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(
            form_frame,
            text=f"➕ إضافة طلب {repair_type}",
            font=('Arial', 16, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_card']
        ).pack(pady=15)

        # حقول النموذج
        fields_frame = tk.Frame(form_frame, bg=self.colors['bg_card'])
        fields_frame.pack(fill=tk.X, padx=20, pady=10)

        # متغيرات النموذج
        self.device_model = tk.StringVar()
        self.problem_desc = tk.StringVar()
        self.customer_name = tk.StringVar()
        self.customer_phone = tk.StringVar()
        self.repair_price = tk.StringVar(value="0")
        self.repair_notes = tk.StringVar()

        # الحقول
        fields = [
            ("📱 موديل الجهاز:", self.device_model),
            ("🔍 وصف المشكلة:", self.problem_desc),
            ("👤 اسم العميل:", self.customer_name),
            ("📞 رقم الهاتف:", self.customer_phone),
            ("💰 السعر:", self.repair_price),
            ("📝 ملاحظات:", self.repair_notes)
        ]

        for i, (label_text, var) in enumerate(fields):
            row_frame = tk.Frame(fields_frame, bg=self.colors['bg_card'])
            row_frame.grid(row=i//2, column=(i%2)*2, padx=10, pady=5, sticky="ew")

            tk.Label(
                row_frame,
                text=label_text,
                font=('Arial', 10, 'bold'),
                fg=self.colors['text_dark'],
                bg=self.colors['bg_card']
            ).pack(anchor='w')

            entry = tk.Entry(
                row_frame,
                textvariable=var,
                font=('Arial', 10),
                width=25
            )
            entry.pack(fill=tk.X, pady=(0, 5))

        fields_frame.grid_columnconfigure(0, weight=1)
        fields_frame.grid_columnconfigure(2, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(form_frame, bg=self.colors['bg_card'])
        buttons_frame.pack(pady=15)

        save_btn = tk.Button(
            buttons_frame,
            text="✅ حفظ طلب الصيانة",
            font=('Arial', 12, 'bold'),
            bg=self.colors['success'],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2',
            command=lambda: self.save_repair_request(repair_type)
        )
        save_btn.pack(side=tk.LEFT, padx=10)

        delete_btn = tk.Button(
            buttons_frame,
            text="🗑️ حذف طلب محدد",
            font=('Arial', 12, 'bold'),
            bg=self.colors['danger'],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2',
            command=lambda: self.delete_repair_request(repair_type)
        )
        delete_btn.pack(side=tk.LEFT, padx=10)

        # جدول الطلبات الحالية
        self.create_repair_requests_table(repair_frame, repair_type)

    def save_repair_request(self, repair_type):
        """حفظ طلب صيانة جديد"""
        # التحقق من البيانات المطلوبة
        if not self.device_model.get() or not self.problem_desc.get():
            messagebox.showerror("خطأ", "يرجى إدخال موديل الجهاز ووصف المشكلة")
            return

        try:
            price = float(self.repair_price.get() or 0)

            # حفظ في قاعدة البيانات
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            self.cursor.execute('''
                INSERT INTO repair_requests
                (date, repair_type, device_model, problem_description, customer_name, customer_phone, price, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                now,
                repair_type.replace('صيانة ', '').lower(),
                self.device_model.get(),
                self.problem_desc.get(),
                self.customer_name.get(),
                self.customer_phone.get(),
                price,
                self.repair_notes.get()
            ))

            self.conn.commit()

            # مسح النموذج
            self.device_model.set("")
            self.problem_desc.set("")
            self.customer_name.set("")
            self.customer_phone.set("")
            self.repair_price.set("0")
            self.repair_notes.set("")

            # تحديث الجدول
            self.load_repair_requests(repair_type)

            # تحديث الإحصائيات
            self.load_daily_transactions()

            messagebox.showinfo("نجح", "تم حفظ طلب الصيانة بنجاح")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def create_repair_requests_table(self, parent, repair_type):
        """إنشاء جدول طلبات الصيانة"""
        table_frame = tk.Frame(parent, bg=self.colors['bg_card'], relief=tk.RAISED, bd=2)
        table_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # عنوان الجدول
        table_title = tk.Label(
            table_frame,
            text=f"📋 طلبات {repair_type} الحالية",
            font=('Arial', 14, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_card']
        )
        table_title.pack(pady=10)

        # جدول الطلبات
        columns = ('التاريخ', 'الجهاز', 'المشكلة', 'العميل', 'السعر')
        self.repair_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=8)

        for col in columns:
            self.repair_tree.heading(col, text=col)
            self.repair_tree.column(col, width=120, anchor='center')

        self.repair_tree.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تحميل الطلبات
        self.load_repair_requests(repair_type)

    def load_repair_requests(self, repair_type):
        """تحميل طلبات الصيانة"""
        # مسح البيانات الحالية
        for item in self.repair_tree.get_children():
            self.repair_tree.delete(item)

        # جلب الطلبات
        repair_type_key = repair_type.replace('صيانة ', '').lower()
        self.cursor.execute('''
            SELECT date, device_model, problem_description, customer_name, price
            FROM repair_requests
            WHERE repair_type = ?
            ORDER BY date DESC
        ''', (repair_type_key,))

        for row in self.cursor.fetchall():
            date_str = row[0].split(' ')[0] if ' ' in row[0] else row[0]
            device = row[1] or 'غير محدد'
            problem = row[2][:30] + '...' if len(row[2]) > 30 else row[2]
            customer = row[3] or 'غير محدد'
            price = f"{row[4]:.2f} جنيه"

            self.repair_tree.insert('', 'end', values=(date_str, device, problem, customer, price))

    def delete_repair_request(self, repair_type):
        """حذف طلب صيانة محدد"""
        selected = self.repair_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار طلب للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا الطلب؟"):
            try:
                # الحصول على بيانات الطلب المحدد
                item = self.repair_tree.item(selected[0])
                values = item['values']

                repair_type_key = repair_type.replace('صيانة ', '').lower()

                # حذف من قاعدة البيانات
                self.cursor.execute('''
                    DELETE FROM repair_requests
                    WHERE repair_type = ? AND device_model = ? AND customer_name = ?
                    ORDER BY date DESC LIMIT 1
                ''', (repair_type_key, values[1], values[3]))

                self.conn.commit()

                # تحديث الجدول
                self.load_repair_requests(repair_type)

                # تحديث الإحصائيات
                self.load_daily_transactions()

                messagebox.showinfo("نجح", "تم حذف الطلب بنجاح")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def show_warehouse_section(self, parent):
        """عرض قسم المخازن"""
        warehouse_frame = tk.Frame(parent, bg=self.colors['bg_main'])
        warehouse_frame.pack(fill=tk.BOTH, expand=True)

        # رسالة المخازن الفارغة
        empty_label = tk.Label(
            warehouse_frame,
            text="📦 المخازن فارغة - جاهزة للتخصيص",
            font=('Arial', 18, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_main']
        )
        empty_label.pack(pady=50)

        # زر إضافة فئة مخزن
        add_btn = tk.Button(
            warehouse_frame,
            text="➕ إضافة فئة مخزن جديدة",
            font=('Arial', 14, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.FLAT,
            padx=30,
            pady=15,
            cursor='hand2',
            command=self.add_warehouse_category
        )
        add_btn.pack(pady=20)

    def add_warehouse_category(self):
        """إضافة فئة مخزن جديدة"""
        messagebox.showinfo("قريباً", "ميزة إضافة فئات المخازن ستكون متاحة قريباً")

    def show_deferred_account_section(self, parent):
        """عرض قسم الحساب المؤجل"""
        deferred_frame = tk.Frame(parent, bg=self.colors['bg_main'])
        deferred_frame.pack(fill=tk.BOTH, expand=True)

        # رسالة الحساب المؤجل
        label = tk.Label(
            deferred_frame,
            text="📋 نظام الحساب المؤجل",
            font=('Arial', 18, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_main']
        )
        label.pack(pady=50)

        # زر إضافة عميل
        add_btn = tk.Button(
            deferred_frame,
            text="➕ إضافة عميل جديد",
            font=('Arial', 14, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.FLAT,
            padx=30,
            pady=15,
            cursor='hand2',
            command=self.add_deferred_customer
        )
        add_btn.pack(pady=20)

    def add_deferred_customer(self):
        """إضافة عميل حساب مؤجل"""
        messagebox.showinfo("قريباً", "ميزة الحساب المؤجل ستكون متاحة قريباً")

    def show_financial_section(self, parent, section_name):
        """عرض الأقسام المالية"""
        financial_frame = tk.Frame(parent, bg=self.colors['bg_main'])
        financial_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان القسم
        title_label = tk.Label(
            financial_frame,
            text=f"💰 {section_name}",
            font=('Arial', 18, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_main']
        )
        title_label.pack(pady=30)

        # نموذج إضافة معاملة
        form_frame = tk.Frame(financial_frame, bg=self.colors['bg_card'], relief=tk.RAISED, bd=2)
        form_frame.pack(pady=20, padx=50, fill=tk.X)

        # متغيرات النموذج
        self.amount_var = tk.StringVar()
        self.notes_var = tk.StringVar()

        # حقل المبلغ
        tk.Label(
            form_frame,
            text="💰 المبلغ:",
            font=('Arial', 12, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_card']
        ).pack(pady=10)

        tk.Entry(
            form_frame,
            textvariable=self.amount_var,
            font=('Arial', 12),
            width=20
        ).pack(pady=5)

        # حقل الملاحظات
        tk.Label(
            form_frame,
            text="📝 ملاحظات:",
            font=('Arial', 12, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_card']
        ).pack(pady=(15, 5))

        tk.Entry(
            form_frame,
            textvariable=self.notes_var,
            font=('Arial', 12),
            width=30
        ).pack(pady=5)

        # زر الحفظ
        save_btn = tk.Button(
            form_frame,
            text="✅ حفظ المعاملة",
            font=('Arial', 12, 'bold'),
            bg=self.colors['success'],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=10,
            cursor='hand2',
            command=lambda: self.save_financial_transaction(section_name)
        )
        save_btn.pack(pady=20)

    def save_financial_transaction(self, section_name):
        """حفظ معاملة مالية"""
        if not self.amount_var.get():
            messagebox.showerror("خطأ", "يرجى إدخال المبلغ")
            return

        try:
            amount = float(self.amount_var.get())
            now = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # الحصول على معرف الفئة
            category_map = {
                'كاش': 'cash',
                'فوري': 'fawry',
                'في الخارج': 'expenses',
                'إضافة سيولة': 'liquidity'
            }

            category_name = category_map.get(section_name, 'cash')

            self.cursor.execute('SELECT id FROM categories WHERE name = ?', (category_name,))
            category_id = self.cursor.fetchone()[0]

            self.cursor.execute('''
                INSERT INTO transactions (date, category_id, quantity, price, total, notes)
                VALUES (?, ?, 1, ?, ?, ?)
            ''', (now, category_id, amount, amount, self.notes_var.get()))

            self.conn.commit()

            # مسح النموذج
            self.amount_var.set("")
            self.notes_var.set("")

            # تحديث الإحصائيات
            self.load_daily_transactions()

            messagebox.showinfo("نجح", f"تم حفظ معاملة {section_name} بنجاح")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

    def show_products_section(self, parent, cat_id, cat_name):
        """عرض قسم المنتجات"""
        products_frame = tk.Frame(parent, bg=self.colors['bg_main'])
        products_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان القسم
        title_label = tk.Label(
            products_frame,
            text=f"🛒 {cat_name}",
            font=('Arial', 18, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_main']
        )
        title_label.pack(pady=30)

        # رسالة مؤقتة
        temp_label = tk.Label(
            products_frame,
            text="قسم المنتجات قيد التطوير",
            font=('Arial', 16),
            fg=self.colors['text_light'],
            bg=self.colors['bg_main']
        )
        temp_label.pack(pady=50)

if __name__ == "__main__":
    print("🏪 بدء تشغيل تطبيق حساب يومية محل الهواتف...")
    try:
        root = tk.Tk()
        print("✅ تم إنشاء النافذة الرئيسية")
        app = PhoneShopApp(root)
        print("✅ تم تحميل التطبيق بنجاح")
        print("🚀 التطبيق جاهز للاستخدام!")
        root.mainloop()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")
