#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق حساب يومية محلات الهواتف - نسخة جديدة نظيفة
Phone Shop Daily Calculator Application - Clean New Version
"""

import tkinter as tk
from tkinter import messagebox
from datetime import datetime

class PhoneShopApp:
    def __init__(self, root):
        self.root = root
        self.root.title("🏪 حساب يومية محل الهواتف")
        
        # ضبط حجم النافذة
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        
        # حساب الأبعاد (90% من الشاشة)
        width = int(screen_width * 0.9)
        height = int(screen_height * 0.9)
        
        # توسيط النافذة
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        self.root.geometry(f"{width}x{height}+{x}+{y}")
        self.root.minsize(1000, 700)
        
        # جعل النافذة تظهر في المقدمة
        self.root.lift()
        self.root.attributes('-topmost', True)
        self.root.after_idle(self.root.attributes, '-topmost', False)
        self.root.focus_force()
        
        # ألوان التطبيق
        self.colors = {
            'primary': '#2E86AB',      # أزرق رئيسي
            'secondary': '#A23B72',    # بنفسجي ثانوي
            'accent': '#F18F01',       # برتقالي للتمييز
            'success': '#27ae60',      # أخضر للنجاح
            'danger': '#e74c3c',       # أحمر للخطر
            'warning': '#f39c12',      # برتقالي للتحذير
            'info': '#3498db',         # أزرق للمعلومات
            'bg_main': '#F5F7FA',      # خلفية رئيسية فاتحة
            'bg_card': '#FFFFFF',      # خلفية البطاقات
            'text_dark': '#2D3748',    # نص داكن
            'text_light': '#718096',   # نص فاتح
            'border': '#E2E8F0'        # حدود
        }
        
        self.root.configure(bg=self.colors['bg_main'])
        
        # إنشاء الواجهة
        self.create_interface()
    
    def create_interface(self):
        """إنشاء الواجهة الأساسية"""
        # مسح أي محتوى موجود
        for widget in self.root.winfo_children():
            widget.destroy()
        
        # الحاوي الرئيسي
        main_container = tk.Frame(self.root, bg=self.colors['bg_main'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان
        self.create_header(main_container)
        
        # المحتوى الرئيسي
        content_frame = tk.Frame(main_container, bg=self.colors['bg_main'])
        content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # رسالة ترحيب
        welcome_label = tk.Label(
            content_frame,
            text="🏪 مرحباً بك في تطبيق حساب يومية محل الهواتف",
            font=('Arial', 24, 'bold'),
            fg=self.colors['text_dark'],
            bg=self.colors['bg_main']
        )
        welcome_label.pack(pady=50)
        
        # رسالة حالة
        status_label = tk.Label(
            content_frame,
            text="✅ التطبيق يعمل بنجاح! جاهز لإضافة الوظائف",
            font=('Arial', 16),
            fg=self.colors['success'],
            bg=self.colors['bg_main']
        )
        status_label.pack(pady=20)
        
        # زر اختبار
        test_btn = tk.Button(
            content_frame,
            text="🔥 اختبار التطبيق",
            font=('Arial', 14, 'bold'),
            bg=self.colors['primary'],
            fg='white',
            relief=tk.FLAT,
            padx=30,
            pady=15,
            cursor='hand2',
            command=self.test_app
        )
        test_btn.pack(pady=30)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=70)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # اسم التطبيق
        title_label = tk.Label(
            header_frame,
            text="🏪 حساب يومية محل الهواتف",
            font=('Arial', 18, 'bold'),
            fg='white',
            bg=self.colors['primary']
        )
        title_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # التاريخ
        today = datetime.now().strftime('%Y-%m-%d')
        date_label = tk.Label(
            header_frame,
            text=f"📅 {today}",
            font=('Arial', 14, 'bold'),
            fg='white',
            bg=self.colors['primary']
        )
        date_label.pack(side=tk.RIGHT, padx=20, pady=20)
    
    def test_app(self):
        """اختبار التطبيق"""
        messagebox.showinfo("نجح!", "🎉 التطبيق يعمل بشكل مثالي!\n\nجاهز لإضافة الوظائف واحدة واحدة.")

if __name__ == "__main__":
    print("🏪 بدء تشغيل تطبيق حساب يومية محل الهواتف...")
    print("📁 العمل في مجلد test2 - نسخة نظيفة")
    
    try:
        root = tk.Tk()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        app = PhoneShopApp(root)
        print("✅ تم تحميل التطبيق بنجاح")
        print("🚀 التطبيق جاهز للاستخدام!")
        
        root.mainloop()
        print("🔚 تم إغلاق التطبيق")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        messagebox.showerror("خطأ", f"حدث خطأ: {e}")
        input("اضغط Enter للخروج...")
