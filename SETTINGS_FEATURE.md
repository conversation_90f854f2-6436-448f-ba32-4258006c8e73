# ⚙️ ميزة الإعدادات وتخصيص التطبيق - الإصدار 4.6

## ✅ الميزات الجديدة المُضافة

### 🎨 **تحسين قسم فلسطين**

#### 📍 **تقريب النص من العلم:**
- **تقليل المسافة**: من padx=15 إلى padx=5
- **محاذاة يسار**: anchor='w' بدلاً من center
- **مظهر أكثر تنظيماً** وتماسكاً

### ⚙️ **زر الإعدادات الجديد**

#### 🔧 **الموقع والتصميم:**
- **موقع**: في يمين قسم فلسطين
- **الرمز**: ⚙️ (علامة ترس)
- **التصميم**: زر مسطح بحجم مناسب
- **التفاعل**: cursor='hand2' للتفاعل

#### 🎯 **الوظيفة:**
- **فتح نافذة إعدادات** منفصلة
- **تخصيص اسم التطبيق**
- **إضافة شعار مخصص**
- **حفظ الإعدادات فورياً**

### 📝 **نافذة الإعدادات**

#### 🖼️ **التصميم:**
- **حجم النافذة**: 500x400 بكسل
- **نافذة منبثقة**: Toplevel مع grab_set
- **ألوان متناسقة** مع ثيم التطبيق
- **تصميم احترافي** ومنظم

#### 🔧 **المكونات:**

##### **1. إعداد اسم التطبيق:**
```python
📝 اسم التطبيق:
┌─────────────────────────────────────────┐
│ حساب يومية محل الهواتف                  │
└─────────────────────────────────────────┘
```

##### **2. إعداد الشعار:**
```python
🖼️ شعار التطبيق:
┌─────────────────────────────────┐ ┌─────────┐
│ مسار الشعار...                  │ │ 📁 تصفح │
└─────────────────────────────────┘ └─────────┘
```

##### **3. أزرار التحكم:**
```python
┌─────────────┐ ┌─────────────┐
│ 💾 حفظ      │ │ ❌ إلغاء    │
└─────────────┘ └─────────────┘
```

### 🏪 **تحديث شريط العنوان**

#### 🎨 **الشعار المخصص:**
- **عرض الشعار**: إذا تم تحديده في الإعدادات
- **حجم مناسب**: 40x40 بكسل
- **موقع**: بجانب اسم التطبيق
- **تحميل آمن**: مع معالجة الأخطاء

#### 📝 **اسم التطبيق المتغير:**
- **استخدام المتغير**: self.app_name
- **تحديث فوري**: عند تغيير الإعدادات
- **عرض ديناميكي**: 🏪 + اسم التطبيق

## 🔧 التفاصيل التقنية

### **قسم فلسطين المحسن:**
```python
# النص قريب من العلم
text_frame.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)
main_text.pack(anchor='w', pady=15)

# زر الإعدادات في اليمين
settings_frame.pack(side=tk.RIGHT, padx=15)
settings_btn = tk.Button(
    settings_frame,
    text="⚙️",
    font=('Arial', 20),
    command=self.show_settings_menu
)
```

### **نافذة الإعدادات:**
```python
def show_settings_menu(self):
    settings_window = tk.Toplevel(self.root)
    settings_window.title("⚙️ إعدادات التطبيق")
    settings_window.geometry("500x400")
    settings_window.transient(self.root)
    settings_window.grab_set()
    
    # حقل اسم التطبيق
    self.name_entry = tk.Entry(...)
    self.name_entry.insert(0, self.app_name)
    
    # حقل الشعار مع زر تصفح
    self.logo_entry = tk.Entry(...)
    browse_btn = tk.Button(..., command=self.browse_logo_file)
```

### **تصفح الملفات:**
```python
def browse_logo_file(self):
    from tkinter import filedialog
    file_path = filedialog.askopenfilename(
        title="اختر شعار التطبيق",
        filetypes=[
            ("ملفات الصور", "*.png *.jpg *.jpeg *.gif *.bmp"),
            ("جميع الملفات", "*.*")
        ]
    )
```

### **حفظ الإعدادات:**
```python
def save_settings(self, settings_window):
    # حفظ اسم التطبيق
    new_name = self.name_entry.get().strip()
    if new_name:
        self.app_name = new_name
    
    # حفظ مسار الشعار
    new_logo = self.logo_entry.get().strip()
    if new_logo:
        self.app_logo = new_logo
    
    # تحديث الواجهة
    self.update_theme()
```

### **عرض الشعار في العنوان:**
```python
# الشعار (إذا كان موجود)
if self.app_logo:
    try:
        import os
        from PIL import Image, ImageTk
        if os.path.exists(self.app_logo):
            logo_image = Image.open(self.app_logo)
            logo_image = logo_image.resize((40, 40), Image.Resampling.LANCZOS)
            logo_photo = ImageTk.PhotoImage(logo_image)
            
            logo_label = tk.Label(title_frame, image=logo_photo)
            logo_label.image = logo_photo
            logo_label.pack(side=tk.LEFT, padx=(0, 10))
    except:
        pass

# عنوان التطبيق
title_label = tk.Label(title_frame, text=f"🏪 {self.app_name}")
```

## 🎨 النتيجة البصرية

### **قسم فلسطين المحسن:**
```
┌─────────────────────────────────────────────────────────────┐
│ 🇵🇸 نحن ندعم فلسطين                              ⚙️      │
│ [علم]  [نص قريب]                                [ترس]     │
└─────────────────────────────────────────────────────────────┘
```

### **نافذة الإعدادات:**
```
⚙️ إعدادات التطبيق

┌─────────────────────────────────────────────────────────────┐
│                                                             │
│ 📝 اسم التطبيق:                                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ حساب يومية محل الهواتف                                │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ 🖼️ شعار التطبيق:                                          │
│ ┌─────────────────────────────────────┐ ┌─────────────────┐ │
│ │ مسار الشعار...                      │ │ 📁 تصفح        │ │
│ └─────────────────────────────────────┘ └─────────────────┘ │
│                                                             │
│ ┌─────────────────┐ ┌─────────────────┐                   │
│ │ 💾 حفظ          │ │ ❌ إلغاء        │                   │
│ └─────────────────┘ └─────────────────┘                   │
└─────────────────────────────────────────────────────────────┘
```

### **شريط العنوان مع الشعار:**
```
┌─────────────────────────────────────────────────────────────┐
│ [🖼️] 🏪 اسم التطبيق المخصص        📅 2024-01-15    🌙    │
│ [شعار] [اسم مخصص]                   [تاريخ]      [ثيم]    │
└─────────────────────────────────────────────────────────────┘
```

## 🎊 المميزات المحققة

### 🎨 **للتصميم:**
- **قسم فلسطين محسن** مع نص قريب من العلم
- **زر إعدادات أنيق** في المكان المناسب
- **نافذة إعدادات احترافية** ومنظمة
- **شعار مخصص** في شريط العنوان

### ⚙️ **للتخصيص:**
- **تغيير اسم التطبيق** بسهولة
- **إضافة شعار مخصص** للتطبيق
- **حفظ فوري** للإعدادات
- **تحديث مباشر** للواجهة

### 🔧 **للوظائف:**
- **تصفح ملفات الصور** للشعار
- **معالجة آمنة** للملفات
- **تحديث ديناميكي** للعنوان
- **واجهة سهلة** للإعدادات

### 👤 **للمستخدم:**
- **تخصيص شخصي** للتطبيق
- **سهولة في التغيير**
- **مظهر احترافي** مخصص
- **تجربة مميزة** وشخصية

## 🌟 النتيجة النهائية

**تم إضافة ميزة الإعدادات بنجاح:**

⚙️ **زر إعدادات** في قسم فلسطين  
📝 **تخصيص اسم التطبيق** بسهولة  
🖼️ **إضافة شعار مخصص** للتطبيق  
🎨 **تحديث فوري** للواجهة  
💾 **حفظ آمن** للإعدادات  
🏪 **مظهر شخصي** ومميز  

## 💡 كيفية الاستخدام

### **الخطوات:**

#### **1. الوصول للإعدادات:**
- **اذهب لأسفل الشاشة الرئيسية**
- **ابحث عن قسم فلسطين**
- **اضغط على زر ⚙️ في اليمين**

#### **2. تخصيص الاسم:**
- **اكتب الاسم الجديد** في حقل "اسم التطبيق"
- **مثال**: "محل أحمد للهواتف"

#### **3. إضافة الشعار:**
- **اضغط على "📁 تصفح"**
- **اختر صورة الشعار** من جهازك
- **الصيغ المدعومة**: PNG, JPG, JPEG, GIF, BMP

#### **4. حفظ التغييرات:**
- **اضغط على "💾 حفظ"**
- **ستظهر رسالة تأكيد**
- **سيتم تحديث الواجهة فوراً**

## 🚀 الخلاصة

**تم إضافة ميزة الإعدادات الشاملة:**

✅ **تحسين قسم فلسطين** مع نص قريب من العلم  
✅ **زر إعدادات أنيق** في المكان المناسب  
✅ **نافذة إعدادات احترافية** مع جميع الخيارات  
✅ **تخصيص اسم التطبيق** بسهولة تامة  
✅ **إضافة شعار مخصص** يظهر في العنوان  
✅ **حفظ فوري وآمن** للإعدادات  

**الآن يمكن تخصيص التطبيق بالكامل! ⚙️**

**تطبيق شخصي، مظهر مميز! 🎨**
